const express = require('express')
const userController = require('../controller/user_view')
const router = express.Router()
const authenticateToken = require('../middleware/authenticateToken')
const {
  filterByUserSchool,
  checkSchoolAccess,
  validateSchoolAssignment,
  restrictGeneralAdminAccess,
} = require('../middleware/auth')
const roleList = require('../helpers/roleList')
const verifyRoles = require('../middleware/verifyRoles')

router
  .route('/all')
  .get(
    authenticateToken,
    verifyRoles(roleList.Admin),
    restrictGeneralAdminAccess,
    userController.getAllUsers
  )

router
  .route('/admin/create')
  .post(
    authenticateToken,
    verifyRoles(roleList.Admin),
    restrictGeneralAdminAccess,
    userController.createAdmin
  )
router
  .route('/ict-admin/all')
  .get(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.Proprietor),
    filterByUserSchool,
    userController.getICT_administrators
  )
router
  .route('/ict-admin/:id/get')
  .get(
    authenticateToken,
    verifyRoles(
      roleList.Admin,
      roleList.Proprietor,
      roleList.ICT_administrator
    ),
    userController.getICT_administrator
  )
router
  .route('/ict-admin/create')
  .post(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.ICT_administrator),
    validateSchoolAssignment,
    userController.createICT_administrator
  )
router
  .route('/ict-admin/:id/update')
  .put(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.ICT_administrator),
    userController.updateICT_administrator
  )
router
  .route('/ict-admin/:id/delete')
  .delete(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.ICT_administrator),
    userController.deleteICT_administrator
  )
router
  .route('/auditors/all')
  .get(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.Proprietor),
    userController.getAuditors
  )
router
  .route('/auditor/:id/get')
  .get(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.Proprietor, roleList.Auditor),
    userController.getAuditor
  )
router
  .route('/auditor/create')
  .post(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.Proprietor, roleList.Auditor),
    validateSchoolAssignment,
    userController.createAuditor
  )
router
  .route('/auditor/:id/update')
  .put(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.Proprietor, roleList.Auditor),
    userController.updateAuditor
  )
router
  .route('/auditor/:id/delete')
  .delete(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.Proprietor, roleList.Auditor),
    userController.deleteAuditor
  )
router
  .route('/proprietors/all')
  .get(
    authenticateToken,
    verifyRoles(roleList.Admin),
    userController.getProprietors
  )
router
  .route('/proprietor/:id/get')
  .get(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.Proprietor),
    userController.getProprietor
  )
router.route('/proprietor/create').post(userController.createProprietor)
router
  .route('/proprietor/:id/update')
  .put(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.Proprietor),
    userController.updateProprietor
  )
router
  .route('/proprietor/:id/delete')
  .delete(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.Proprietor),
    userController.deleteProprietor
  )
router
  .route('/principals/all')
  .get(
    authenticateToken,
    verifyRoles(roleList.Admin),
    userController.getPrincipals
  )
router
  .route('/principal/:id/get')
  .get(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.Principal),
    userController.getPrincipal
  )
router
  .route('/principal/create')
  .post(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.Principal),
    userController.createPrincipal
  )
router
  .route('/principal/:id/update')
  .put(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.Principal),
    userController.updatePrincipal
  )
router
  .route('/principal/:id/delete')
  .delete(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.Principal),
    userController.deletePrincipal
  )
router
  .route('/headteachers/all')
  .get(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.Principal),
    userController.getHeadteachers
  )
router
  .route('/headteacher/:id/get')
  .get(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.Headteacher, roleList.Principal),
    userController.getHeadteacher
  )
router.route('/headteacher/create').post(userController.createHeadteacher)
router
  .route('/headteacher/:id/update')
  .put(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.Headteacher, roleList.Principal),
    userController.updateHeadteacher
  )
router
  .route('/headteacher/:id/delete')
  .delete(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.Headteacher, roleList.Principal),
    userController.deleteHeadteacher
  )
router
  .route('/bursars/all')
  .get(
    authenticateToken,
    verifyRoles(roleList.Admin),
    userController.getBursars
  )
router
  .route('/bursar/:id/get')
  .get(
    authenticateToken,
    verifyRoles(
      roleList.Admin,
      roleList.Proprietor,
      roleList.Principal,
      roleList.Bursar
    ),
    userController.getBursar
  )
router.route('/bursar/create').post(userController.createBursar)
router
  .route('/bursar/:id/update')
  .put(
    authenticateToken,
    verifyRoles(
      roleList.Admin,
      roleList.Proprietor,
      roleList.Principal,
      roleList.Bursar
    ),
    userController.updateBursar
  )
router
  .route('/bursar/:id/delete')
  .delete(
    authenticateToken,
    verifyRoles(
      roleList.Admin,
      roleList.Proprietor,
      roleList.Principal,
      roleList.Bursar
    ),
    userController.deleteBursar
  )
router
  .route('/student/all')
  .get(
    authenticateToken,
    verifyRoles(
      roleList.Admin,
      roleList.Proprietor,
      roleList.Principal,
      roleList.Headteacher,
      roleList.ICT_administrator,
      roleList.Bursar
    ),
    filterByUserSchool,
    userController.getStudents
  )
router
  .route('/student/:school_id/get')
  .get(
    authenticateToken,
    verifyRoles(
      roleList.Admin,
      roleList.Proprietor,
      roleList.ICT_administrator,
      roleList.Principal
    ),
    checkSchoolAccess,
    userController.getStudentsInParticularSchool
  )
router
  .route('/student/:id/get')
  .get(
    authenticateToken,
    verifyRoles(
      roleList.Admin,
      roleList.Proprietor,
      roleList.ICT_administrator,
      roleList.Principal
    ),
    userController.getStudent
  )
router
  .route('/student/create')
  .post(
    authenticateToken,
    verifyRoles(
      roleList.Admin,
      roleList.Proprietor,
      roleList.ICT_administrator,
      roleList.Principal
    ),
    validateSchoolAssignment,
    userController.createStudent
  )
router
  .route('/student/:id/update')
  .put(
    authenticateToken,
    verifyRoles(
      roleList.Admin,
      roleList.Proprietor,
      roleList.ICT_administrator,
      roleList.Principal
    ),
    userController.updateStudent
  )
router
  .route('/student/:id/delete')
  .delete(
    authenticateToken,
    verifyRoles(
      roleList.Admin,
      roleList.Proprietor,
      roleList.ICT_administrator,
      roleList.Principal
    ),
    userController.deleteStudent
  )
router
  .route('/parents/all')
  .get(
    authenticateToken,
    verifyRoles(
      roleList.Admin,
      roleList.Proprietor,
      roleList.Principal,
      roleList.Headteacher,
      roleList.ICT_administrator
    ),
    filterByUserSchool,
    userController.getParents
  )
router
  .route('/parent/:school_id/get')
  .get(
    authenticateToken,
    verifyRoles(
      roleList.Admin,
      roleList.Proprietor,
      roleList.Principal,
      roleList.Headteacher,
      roleList.ICT_administrator
    ),
    checkSchoolAccess,
    userController.getParentsInParticularSchool
  )
router
  .route('/parent/:id/get')
  .get(
    authenticateToken,
    verifyRoles(
      roleList.Admin,
      roleList.Proprietor,
      roleList.Principal,
      roleList.Headteacher,
      roleList.ICT_administrator,
      roleList.Parent
    ),
    userController.getParent
  )
router
  .route('/parent/create')
  .post(
    authenticateToken,
    verifyRoles(
      roleList.Admin,
      roleList.Proprietor,
      roleList.Principal,
      roleList.Headteacher,
      roleList.ICT_administrator,
      roleList.Parent
    ),
    validateSchoolAssignment,
    userController.createParent
  )
router
  .route('/parent/:id/update')
  .put(
    authenticateToken,
    verifyRoles(
      roleList.Admin,
      roleList.Proprietor,
      roleList.Principal,
      roleList.Headteacher,
      roleList.ICT_administrator,
      roleList.Parent
    ),
    userController.updateParent
  )
router
  .route('/parent/:id/delete')
  .delete(
    authenticateToken,
    verifyRoles(
      roleList.Admin,
      roleList.Proprietor,
      roleList.Principal,
      roleList.Headteacher,
      roleList.ICT_administrator,
      roleList.Parent
    ),
    userController.deleteParent
  )
router.get('/staff/:school_id', userController.getStaffBySchool)

// Get current user profile
router.route('/profile').get(authenticateToken, userController.getCurrentUser)

// Bulk operations
router
  .route('/bulk-delete')
  .post(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.ICT_administrator),
    userController.bulkDeleteUsers
  )

router
  .route('/bulk-update')
  .post(
    authenticateToken,
    verifyRoles(roleList.Admin, roleList.ICT_administrator),
    userController.bulkUpdateUsers
  )

module.exports = router
